import 'package:build_mate/presentation/screens/home/<USER>/tabs/shops_components/subcategory_filter_sheet.dart';
import 'package:build_mate/presentation/state/shop_products_state.dart';
import 'package:build_mate/presentation/view_models/user/shop_products_view_model.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'dart:async';

class SearchProductTextField extends ConsumerStatefulWidget {
  const SearchProductTextField({super.key});
  @override
  ConsumerState<SearchProductTextField> createState() =>
      _SearchProductTextFieldState();
}

// Add this widget below your existing code

class _SearchProductTextFieldState
    extends ConsumerState<SearchProductTextField> {
  Timer? _debounce;

  void _onChanged(String value, void Function(String) onSearchProduct) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      onSearchProduct(value);
    });
  }

  Future<void> _showSubcategoriesSheet(
    BuildContext context,
    ShopProductsState state,
  ) async {
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);
    final isDarkMode = themeMode == ThemeMode.dark;

    showModalBottomSheet(
      context: context,
      backgroundColor: isDarkMode ? customColors.surfaceVariant : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      isScrollControlled: true,
      builder: (_) {
        return FractionallySizedBox(
          widthFactor: 1,
          heightFactor: 0.9,
          child: SubcategoryFilterSheet(subcategories: state.subcategories),
        );
      },
    );
  }

  @override
  void dispose() {
    _debounce?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final viewModel = ref.watch(shopProductsViewModelProvider.notifier);
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);
    final isDarkMode = themeMode == ThemeMode.dark;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color:
            isDarkMode
                ? customColors.surfaceVariant.withValues(alpha: 0.8)
                : Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border:
            isDarkMode
                ? Border.all(
                  color: customColors.textPrimaryColor.withValues(alpha: 0.1),
                  width: 1,
                )
                : null,
      ),
      child: Row(
        children: [
          Icon(
            Icons.search,
            color: customColors.textPrimaryColor.withValues(alpha: 0.6),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Find your products',
                hintStyle: TextStyle(
                  color: customColors.textPrimaryColor.withValues(alpha: 0.5),
                  fontSize: 16,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
              onChanged:
                  (value) => _onChanged(value, viewModel.onSearchProduct),
              style: TextStyle(
                fontSize: 16,
                color: customColors.textPrimaryColor,
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              _showSubcategoriesSheet(
                context,
                ref.watch(shopProductsViewModelProvider),
              );
            },
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color:
                    isDarkMode
                        ? customColors.textPrimaryColor.withValues(alpha: 0.1)
                        : Colors.grey[200],
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                Icons.tune,
                color: customColors.textPrimaryColor.withValues(alpha: 0.7),
                size: 18,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
