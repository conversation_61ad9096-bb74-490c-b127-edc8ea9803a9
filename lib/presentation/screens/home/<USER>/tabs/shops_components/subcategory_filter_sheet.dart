import 'package:build_mate/presentation/view_models/user/shop_products_view_model.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SubcategoryFilterSheet extends ConsumerStatefulWidget {
  final List<dynamic> subcategories;
  const SubcategoryFilterSheet({super.key, required this.subcategories});

  @override
  ConsumerState<SubcategoryFilterSheet> createState() =>
      _SubcategoryFilterSheetState();
}

class _SubcategoryFilterSheetState
    extends ConsumerState<SubcategoryFilterSheet> {
  @override
  Widget build(BuildContext context) {
    final state = ref.watch(shopProductsViewModelProvider);
    final viewModel = ref.watch(shopProductsViewModelProvider.notifier);

    final filtered =
        state.search.isEmpty
            ? state.subcategories
            : state.subcategories
                .where(
                  (s) => (s['name'] ?? '').toString().toLowerCase().contains(
                    state.search.toLowerCase(),
                  ),
                )
                .toList();

    return SafeArea(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with Filter and Close button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Text(
                  'Filter',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Spacer(),
                IconButton(
                  icon: Icon(Icons.close),
                  onPressed: () {
                    viewModel.getProductsByFilter(
                      state.selectedSubcategoryId ?? 0,
                    );
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
          // Search TextField
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search subcategories',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.white,
                prefixIcon: Icon(Icons.search, color: Colors.grey[600]),
                contentPadding: EdgeInsets.symmetric(
                  vertical: 0,
                  horizontal: 8,
                ),
              ),
              onChanged: (val) => viewModel.filterSubCategory(val),
            ),
          ),
          Divider(
            height: 1,
            color: Colors.grey.withAlpha((0.15 * 255).round()),
          ),
          Expanded(
            child: ListView.separated(
              shrinkWrap: true,
              itemCount: filtered.length,
              separatorBuilder:
                  (_, __) => Divider(
                    height: 1,
                    color: Colors.grey.withAlpha((0.15 * 255).round()),
                  ),
              itemBuilder: (context, index) {
                final sub = filtered[index];
                final isSelected = state.selectedSubcategoryId == sub['id'];

                return AnimatedContainer(
                  duration: Duration(milliseconds: 200),
                  curve: Curves.ease,
                  decoration: BoxDecoration(
                    color:
                        isSelected
                            ? Colors.blue.withAlpha((0.08 * 255).round())
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ListTile(
                    title: Text(
                      sub['name'] ?? '',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey[900],
                      ),
                    ),
                    trailing:
                        isSelected
                            ? Icon(Icons.check, color: Colors.blue)
                            : null,
                    onTap: () {
                      viewModel.selectSubcategory(sub['id']);
                      viewModel.setSelectedSubCatagoryName(sub['name']);
                    },
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 2,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    hoverColor: Colors.grey[100],
                    splashColor: Colors.grey[200],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
